<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Improved Scrolling Behavior</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .chat-header {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
        }
        
        #chat-box {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .user-message {
            justify-content: flex-end;
        }
        
        .assistant-message {
            justify-content: flex-start;
        }
        
        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .user-message .message-content {
            background: #007bff;
            color: white;
        }
        
        .assistant-message .message-content {
            background: #e9ecef;
            color: #333;
        }
        
        .input-area {
            padding: 20px;
            display: flex;
            gap: 10px;
        }
        
        #message-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }
        
        #send-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
        }
        
        #send-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .test-buttons {
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        
        .test-button {
            margin: 5px;
            padding: 10px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .spinner {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .info-panel {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="info-panel">
        <h3>Improved Scrolling Behavior Test</h3>
        <p><strong>Expected Behavior:</strong> When multiple AI messages are sent in sequence, the page should scroll to the bottom of the <em>first</em> AI response message, not all the way to the bottom. This allows users to read through the AI's responses sequentially.</p>
        <p><strong>Test Instructions:</strong> Use the test buttons below to simulate different scenarios and observe the scrolling behavior.</p>
    </div>

    <div class="chat-container">
        <div class="chat-header">
            <h2>AI Assistant Chat - Scrolling Test</h2>
        </div>
        
        <div id="chat-box">
            <div class="message assistant-message">
                <div class="message-content">
                    Hello! I'm your AI assistant. Try the test buttons below to see the improved scrolling behavior in action.
                </div>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" id="message-input" placeholder="Type your message here..." />
            <button id="send-button">Send</button>
        </div>
        
        <div class="test-buttons">
            <button class="test-button" onclick="testSingleResponse()">Test Single Response</button>
            <button class="test-button" onclick="testMultipleResponses()">Test Multiple Responses</button>
            <button class="test-button" onclick="testThinkingIndicator()">Test Thinking + Response</button>
            <button class="test-button" onclick="clearChat()">Clear Chat</button>
        </div>
    </div>

    <script>
        // Track AI message sequences for improved scrolling behavior
        let isFirstAIMessageInSequence = true;
        let lastUserMessageTime = 0;

        // Enhanced addMessage function with improved scrolling behavior
        function addMessage(content, role, isHtml = false, shouldScroll = true) {
            const chatBox = document.getElementById('chat-box');
            if (!chatBox) return null;

            // Track message sequence for scrolling behavior
            if (role === 'user') {
                isFirstAIMessageInSequence = true;
                lastUserMessageTime = Date.now();
            }

            // Create the message container div
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message', role === 'user' ? 'user-message' : 'assistant-message');

            // Create message content span
            const messageContentSpan = document.createElement('div');
            messageContentSpan.classList.add('message-content');

            // Set the content
            if (isHtml) {
                messageContentSpan.innerHTML = content;
            } else {
                messageContentSpan.textContent = content;
            }

            messageDiv.appendChild(messageContentSpan);
            chatBox.appendChild(messageDiv);
            
            // Improved scrolling behavior for AI message sequences
            if (shouldScroll) {
                requestAnimationFrame(() => {
                    if (role === 'assistant') {
                        // For AI messages, check if this is the first in a sequence
                        if (isFirstAIMessageInSequence) {
                            // Scroll to the bottom of this first AI message, not the input field
                            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
                            isFirstAIMessageInSequence = false;
                            console.log('Scrolled to first AI message in sequence');
                        } else {
                            console.log('Skipped scrolling for subsequent AI message');
                        }
                        // Subsequent AI messages in the sequence don't trigger auto-scroll
                    } else {
                        // For user messages, scroll to the input field as before
                        const messageInput = document.getElementById('message-input');
                        if (messageInput) {
                            messageInput.scrollIntoView({ behavior: 'smooth', block: 'end' });
                        }
                    }
                });
            }

            return messageDiv;
        }

        // Test functions
        function testSingleResponse() {
            addMessage("Test single response", 'user');
            setTimeout(() => {
                addMessage("This is a single AI response. The page should scroll to show this message.", 'assistant');
            }, 500);
        }

        function testMultipleResponses() {
            addMessage("Test multiple responses", 'user');
            setTimeout(() => {
                addMessage("This is the first AI response. The page should scroll to show this message.", 'assistant');
            }, 500);
            setTimeout(() => {
                addMessage("This is the second AI response. The page should NOT auto-scroll for this message.", 'assistant');
            }, 1500);
            setTimeout(() => {
                addMessage("This is the third AI response. The page should still NOT auto-scroll.", 'assistant');
            }, 2500);
        }

        function testThinkingIndicator() {
            addMessage("Test thinking indicator", 'user');
            
            // Add thinking indicator (should not scroll)
            setTimeout(() => {
                const thinkingId = `thinking-${Date.now()}`;
                const thinkingMessage = addMessage(`<div id="${thinkingId}"><span class="spinner"></span> Thinking...</div>`, 'assistant', true, false);
                
                // Remove thinking indicator and add real response
                setTimeout(() => {
                    if (thinkingMessage) {
                        thinkingMessage.remove();
                        // Reset flag so next message is treated as first in sequence
                        isFirstAIMessageInSequence = true;
                    }
                    addMessage("Here's my response after thinking. The page should scroll to show this message.", 'assistant');
                }, 2000);
            }, 500);
        }

        function clearChat() {
            const chatBox = document.getElementById('chat-box');
            chatBox.innerHTML = `
                <div class="message assistant-message">
                    <div class="message-content">
                        Chat cleared! Try the test buttons to see the improved scrolling behavior.
                    </div>
                </div>
            `;
            isFirstAIMessageInSequence = true;
        }

        // Handle manual message sending
        document.getElementById('send-button').addEventListener('click', function() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            if (message) {
                addMessage(message, 'user');
                input.value = '';
                
                // Simulate AI response
                setTimeout(() => {
                    addMessage(`You said: "${message}". This is my response.`, 'assistant');
                }, 1000);
            }
        });

        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('send-button').click();
            }
        });
    </script>
</body>
</html>
